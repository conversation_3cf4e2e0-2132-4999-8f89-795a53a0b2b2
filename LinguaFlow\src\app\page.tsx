"use client";

import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { AppShell } from '@/components/layout/app-shell';
import {
  EnhancedDocumentEditor,
  DocumentToolbar,
  DocumentStatusBar,
  WritingSuggestionsPanel,
  type EnhancedSuggestion,
  type PlagiarismSource,
  type SimilaritySource
} from '@/components/editor';
import { AiToneAnalyzer } from '@/components/ai/ai-tone-analyzer';
import { AiTextGenerator } from '@/components/ai/ai-text-generator';
import { AiRewriter } from '@/components/ai/ai-rewriter';
import { PlagiarismDetector } from '@/components/ai/plagiarism-detector';
import { AiWritingDetector } from '@/components/ai/ai-writing-detector';
import { HumanizeAiText } from '@/components/ai/humanize-ai-text';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { useI18n } from '@/contexts/i18n-context';
import { useFeatureSettings } from '@/contexts/feature-settings-context';
import { type PlagiarismDetectionOutput } from '@/ai/flows/plagiarism-detection-flow';
// Removed direct imports of server actions - now using API routes
type TextAnalysisInput = {
  text: string;
  language: string;
};

type AnalysisSuggestion = {
  id: string;
  type: 'style' | 'spelling' | 'grammar' | 'rewrite';
  message: string;
  suggestion: string;
  originalSegment: string;
  severity: 'low' | 'medium' | 'high';
  startIndex?: number;
  endIndex?: number;
  suggestions?: string[];
};

type EnhancedTextAnalysisInput = {
  text: string;
  language: string;
  focusAreas?: ('vocabulary' | 'rhythm' | 'flow' | 'engagement' | 'clarity')[];
  avoidRepetition?: boolean;
};

type EnhancedSuggestionType = {
  type: string;
  message: string;
  severity: 'low' | 'medium' | 'high';
  startIndex: number;
  endIndex: number;
  suggestions: string[];
  confidence: number;
  category: string;
};
import { APP_WRITING_LANGUAGES } from '@/config/languages';
import { Edit3, Wand2, ShieldCheck, BrainCircuit, UserCheck, Gauge } from 'lucide-react';
// Removed type import - now defined locally

// Debounce function
function debounce<F extends (...args: any[]) => any>(func: F, waitFor: number): (...args: Parameters<F>) => void {
  let timeout: ReturnType<typeof setTimeout>;

  const debounced = (...args: Parameters<F>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), waitFor);
  };

  return debounced;
}

interface LinguaFlowPageProps {
  writingMode: string;
}

// Types are imported from enhanced-document-editor

function LinguaFlowPage({ writingMode }: LinguaFlowPageProps) {
  const { t, getWritingLanguageBase, setWritingLanguageDialect } = useI18n();
  const { toast } = useToast();
  const { settings } = useFeatureSettings();

  const [editorValue, setEditorValue] = useState("");
  const history = useRef<{ past: string[]; present: string; future: string[] }>({ past: [], present: "", future: [] });
  const [canUndo, setCanUndo] = useState(false);
  const [canRedo, setCanRedo] = useState(false);
  const isUndoRedoAction = useRef(false);

  const [writingDirection, setWritingDirection] = useState<'ltr' | 'rtl'>('ltr');

  // Document formatting states
  const [fontSize, setFontSize] = useState(16);
  const [fontFamily, setFontFamily] = useState("'Inter', sans-serif");

  // Document statistics
  const [wordCount, setWordCount] = useState(0);
  const [charCount, setCharCount] = useState(0);
  const [readingTime, setReadingTime] = useState(0);
  const [writingScore, setWritingScore] = useState(0);

  // Enhanced suggestions state
  const [enhancedSuggestions, setEnhancedSuggestions] = useState<EnhancedSuggestion[]>([]);
  const [isAnalyzingText, setIsAnalyzingText] = useState(false);
  const [plagiarismResult, setPlagiarismResult] = useState<PlagiarismDetectionOutput | null>(null);

  // Legacy compatibility
  const [analysisSuggestions, setAnalysisSuggestions] = useState<AnalysisSuggestion[]>([]);

  const plagiarismSuggestions = useMemo<PlagiarismSource[]>((): PlagiarismSource[] => {
    if (!plagiarismResult || !plagiarismResult.detectedSources) return [];

    return plagiarismResult.detectedSources.map((source, index) => ({
      id: `plagiarism-${index}-${source.startIndex}`,
      type: 'style' as const, // Use compatible type
      category: 'plagiarism' as const,
      originalSegment: source.plagiarizedSegment,
      message: `Potential plagiarism detected. Source: ${source.originalSource} (Similarity: ${source.similarityScore}%)`,
      suggestion: source.originalSource,
      startIndex: source.startIndex,
      endIndex: source.endIndex,
      sourceUrl: source.originalSource,
      similarityScore: source.similarityScore,
      confidence: source.similarityScore / 100,
      severity: source.similarityScore > 80 ? 'high' as const : source.similarityScore > 60 ? 'medium' as const : 'low' as const,
    }));
  }, [plagiarismResult]);

  // Convert legacy suggestions to enhanced format
  const convertedSuggestions = useMemo<EnhancedSuggestion[]>(() => {
    return analysisSuggestions.map((suggestion) => ({
      ...suggestion,
      category: suggestion.type as 'spelling' | 'grammar' | 'style' | 'vocabulary',
      confidence: 0.8, // Default confidence
      severity: 'medium' as const,
    }));
  }, [analysisSuggestions]);

  // Combine all enhanced suggestions
  const allEnhancedSuggestions = useMemo(() => {
    return [...convertedSuggestions, ...plagiarismSuggestions, ...enhancedSuggestions];
  }, [convertedSuggestions, plagiarismSuggestions, enhancedSuggestions]);
  
  // History and Undo/Redo Logic
  const updateHistory = useCallback((value: string) => {
    if (isUndoRedoAction.current) {
        isUndoRedoAction.current = false;
        return;
    }
    const { past, present } = history.current;
    if (value === present) return;
    
    history.current = {
        past: [...past, present],
        present: value,
        future: [],
    };
    setCanUndo(history.current.past.length > 0);
    setCanRedo(history.current.future.length > 0);
  }, []);

  const debouncedUpdateHistory = useCallback(debounce(updateHistory, 800), [updateHistory]);

  const handleEditorChange = useCallback((value: string) => {
    setEditorValue(value);
    debouncedUpdateHistory(value);
  }, [debouncedUpdateHistory]);

  const handleUndo = useCallback(() => {
    const { past, present, future } = history.current;
    if (past.length === 0) return;
    isUndoRedoAction.current = true;
    const newPresent = past[past.length - 1];
    const newPast = past.slice(0, past.length - 1);
    history.current = {
        past: newPast,
        present: newPresent,
        future: [present, ...future],
    };
    setEditorValue(newPresent);
    setCanUndo(newPast.length > 0);
    setCanRedo(true);
  }, []);

  const handleRedo = useCallback(() => {
      const { past, present, future } = history.current;
      if (future.length === 0) return;
      isUndoRedoAction.current = true;
      const newPresent = future[0];
      const newFuture = future.slice(1);
      history.current = {
          past: [...past, present],
          present: newPresent,
          future: newFuture,
      };
      setEditorValue(newPresent);
      setCanUndo(true);
      setCanRedo(newFuture.length > 0);
  }, []);

  // Enhanced Text Analysis Logic
  const requestEnhancedTextAnalysis = useCallback(async (textToAnalyze: string, lang: string) => {
    if (!textToAnalyze.trim()) {
      setEnhancedSuggestions([]);
      setIsAnalyzingText(false);
      return;
    }
    setIsAnalyzingText(true);
    try {
      const analysisInput: EnhancedTextAnalysisInput = {
        text: textToAnalyze,
        language: lang,
        focusAreas: ['vocabulary', 'rhythm', 'flow', 'engagement'],
        avoidRepetition: true,
      };
      const response = await fetch('/api/ai/analyze-text-enhanced', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(analysisInput),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      if (result) {
        // Enhanced suggestions should already have compatible categories
        const mappedSuggestions = result.suggestions || [];
        setEnhancedSuggestions(mappedSuggestions);
        // Update writing score based on enhanced analysis
        setWritingScore(result.overallScore);
      }
    } catch (error) {
      console.error("Error analyzing text for enhanced suggestions:", error);
      toast({ titleKey: "toastErrorTitle", descriptionKey: "toastTextAnalysisError", variant: "destructive" });
      setEnhancedSuggestions([]);
    } finally {
      setIsAnalyzingText(false);
    }
  }, [toast]);

  // Legacy Text Analysis Logic (for compatibility)
  const requestTextAnalysis = useCallback(async (textToAnalyze: string, lang: string) => {
    if (!textToAnalyze.trim()) {
      setAnalysisSuggestions([]);
      return;
    }
    try {
      const analysisInput: TextAnalysisInput = {
        text: textToAnalyze,
        language: lang,
      };
      const response = await fetch('/api/ai/analyze-text', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(analysisInput),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      setAnalysisSuggestions(result.suggestions || []);
    } catch (error) {
      console.error("Error analyzing text for suggestions:", error);
      toast({ titleKey: "toastErrorTitle", descriptionKey: "toastTextAnalysisError", variant: "destructive" });
      setAnalysisSuggestions([]);
    }
  }, [toast]);
  
  const debouncedRequestEnhancedTextAnalysis = useCallback(debounce(requestEnhancedTextAnalysis, 1500), [requestEnhancedTextAnalysis]);
  const debouncedRequestTextAnalysis = useCallback(debounce(requestTextAnalysis, 1500), [requestTextAnalysis]);

  useEffect(() => {
    // Use enhanced analysis for better suggestions
    debouncedRequestEnhancedTextAnalysis(editorValue, getWritingLanguageBase());
    // Also run legacy analysis for compatibility with existing components
    debouncedRequestTextAnalysis(editorValue, getWritingLanguageBase());
  }, [editorValue, getWritingLanguageBase, debouncedRequestEnhancedTextAnalysis, debouncedRequestTextAnalysis]);

  // Language Detection Logic
  const handleLanguageDetection = useCallback(async (text: string) => {
    if (text.trim().length < 50) return; 

    try {
        const response = await fetch('/api/ai/detect-language', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ text }),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        const detectedLangCode = result.languageCode;
        const currentBaseLang = getWritingLanguageBase();

        if (detectedLangCode && detectedLangCode !== 'unknown' && detectedLangCode !== currentBaseLang) {
            const langInfo = APP_WRITING_LANGUAGES.find(l => l.value === detectedLangCode);
            if (langInfo) {
                const newDialect = langInfo.dialects && langInfo.dialects.length > 0
                    ? langInfo.dialects[0].value
                    : langInfo.value;
                
                setWritingLanguageDialect(newDialect);
                
                toast({
                    titleKey: "toastInfoTitle",
                    descriptionKey: "toastLanguageSwitched",
                    descriptionParams: { language: t(langInfo.labelKey) }
                });
            }
        }
    } catch (error) {
        console.error("Automatic language detection failed:", error);
    }
  }, [getWritingLanguageBase, setWritingLanguageDialect, toast, t]);

  const debouncedLanguageDetection = useCallback(debounce(handleLanguageDetection, 2000), [handleLanguageDetection]);

  useEffect(() => {
    const currentBaseLang = getWritingLanguageBase();
    const langInfo = APP_WRITING_LANGUAGES.find(l => l.value === currentBaseLang);
    if (langInfo) {
        setWritingDirection(langInfo.dir || 'ltr');
    }

    if (settings.enableAutomaticLanguageDetection) {
      debouncedLanguageDetection(editorValue);
    }
  }, [editorValue, debouncedLanguageDetection, settings.enableAutomaticLanguageDetection, getWritingLanguageBase]);

  // Editor stats
  useEffect(() => {
    const trimmedValue = editorValue.trim();
    const words = trimmedValue ? trimmedValue.split(/\s+/).filter(Boolean) : [];
    setWordCount(words.length);
    setCharCount(editorValue.length);

    // Calculate reading time (average 200 words per minute)
    const readingTimeMinutes = Math.ceil(words.length / 200);
    setReadingTime(readingTimeMinutes);

    // Calculate writing score based on various factors
    const lengthScore = Math.min(30, Math.floor(words.length / 10));
    const issueCount = allEnhancedSuggestions.filter(s => s.category === 'spelling' || s.category === 'grammar').length;
    const issueDeduction = Math.min(40, issueCount * 5);
    const score = Math.max(0, Math.min(100, lengthScore + 50 - issueDeduction));
    setWritingScore(score);
  }, [editorValue, allEnhancedSuggestions]);

  const handleApplySuggestion = useCallback((suggestion: EnhancedSuggestion) => {
    let newValue = editorValue;
    let applied = false;
    const { suggestion: suggestionText, originalSegment, startIndex, endIndex } = suggestion;

    if (typeof startIndex === 'number' && typeof endIndex === 'number' && endIndex >= startIndex) {
        const textBefore = editorValue.substring(0, startIndex);
        const textAfter = editorValue.substring(endIndex);
        if (editorValue.substring(startIndex, endIndex) === originalSegment) {
            newValue = textBefore + suggestionText + textAfter;
            applied = true;
        }
    }
    if (!applied) {
        // Fallback to first occurrence if indexed replacement fails
        const firstOccurrenceIndex = editorValue.indexOf(originalSegment);
        if (firstOccurrenceIndex !== -1) {
            newValue = editorValue.substring(0, firstOccurrenceIndex) + suggestionText + editorValue.substring(firstOccurrenceIndex + originalSegment.length);
            applied = true;
        }
    }
    if (applied) {
        handleEditorChange(newValue);
        toast({ titleKey: "toastSuccessTitle", descriptionKey: "toastSuggestionAppliedSuccess" });
    } else {
        toast({ titleKey: "toastErrorTitle", descriptionKey: "toastSuggestionApplyError", variant: "destructive" });
    }
  }, [editorValue, toast, handleEditorChange]);

  const handleDismissSuggestion = useCallback((suggestionId: string) => {
    setAnalysisSuggestions(prev => prev.filter(s => s.id !== suggestionId));
    toast({ titleKey: "toastInfoTitle", descriptionKey: "toastSuggestionDismissed" });
  }, [toast]);

  // Legacy wrapper for WritingSuggestionsPanel
  const handleLegacyApplySuggestion = useCallback((suggestionText: string, originalSegment: string, startIndex?: number, endIndex?: number) => {
    const legacySuggestion: EnhancedSuggestion = {
      id: `legacy-${Date.now()}`,
      type: 'style',
      category: 'style',
      message: 'Legacy suggestion',
      suggestion: suggestionText,
      originalSegment,
      startIndex,
      endIndex,
      confidence: 0.8,
      severity: 'medium'
    };
    handleApplySuggestion(legacySuggestion);
  }, [handleApplySuggestion]);
  
  const handleInsertGeneratedText = useCallback((textToInsert: string) => {
    const newText = editorValue.trim() === "" ? textToInsert : `${editorValue}\n\n${textToInsert}`;
    handleEditorChange(newText);
  }, [editorValue, handleEditorChange]);

  const handleApplyRewrite = useCallback((newText: string) => {
    handleEditorChange(newText);
  }, [handleEditorChange]);

  // Calculate suggestion counts for status bar
  const suggestionCounts = useMemo(() => {
    const counts = {
      spelling: 0,
      grammar: 0,
      style: 0,
      vocabulary: 0,
      plagiarism: 0,
      similarity: 0,
    };

    allEnhancedSuggestions.forEach(suggestion => {
      counts[suggestion.category]++;
    });

    return counts;
  }, [allEnhancedSuggestions]);

  return (
      <div className="flex-1 flex flex-col h-full">
        {/* Document Toolbar */}
        <DocumentToolbar
          onUndo={handleUndo}
          onRedo={handleRedo}
          canUndo={canUndo}
          canRedo={canRedo}
          fontSize={fontSize}
          onFontSizeChange={setFontSize}
          fontFamily={fontFamily}
          onFontFamilyChange={setFontFamily}
          writingMode={writingMode}
          onWritingModeChange={(mode: string) => {
            // TODO: Implement writing mode change logic
            console.log('Writing mode changed to:', mode);
          }}
        />

        {/* Main Content Area */}
        <div className="flex-1 grid grid-cols-1 lg:grid-cols-4 gap-6 p-4 md:p-6 overflow-hidden">
          {/* Enhanced Document Editor */}
          <div className="lg:col-span-3 h-full">
            <EnhancedDocumentEditor
              value={editorValue}
              onChange={handleEditorChange}
              writingMode={writingMode}
              direction={writingDirection}
              suggestions={allEnhancedSuggestions.filter(s => s.category !== 'plagiarism' && s.category !== 'similarity')}
              plagiarismSources={plagiarismSuggestions}
              similaritySources={[]} // TODO: Implement similarity detection
              onApplySuggestion={handleApplySuggestion}
              onDismissSuggestion={handleDismissSuggestion}
              onUndo={handleUndo}
              onRedo={handleRedo}
              canUndo={canUndo}
              canRedo={canRedo}
              className="h-full"
            />
          </div>

          {/* Writing Suggestions Panel */}
          <div className="h-full">
            <WritingSuggestionsPanel
              suggestions={analysisSuggestions}
              isAnalyzing={isAnalyzingText}
              onApplySuggestion={handleLegacyApplySuggestion}
              onDismissSuggestion={handleDismissSuggestion}
            />
          </div>
        </div>

        {/* Document Status Bar */}
        <DocumentStatusBar
          wordCount={wordCount}
          charCount={charCount}
          readingTime={readingTime}
          writingScore={writingScore}
          spellingErrors={suggestionCounts.spelling}
          grammarErrors={suggestionCounts.grammar}
          styleIssues={suggestionCounts.style}
          vocabularyEnhancements={suggestionCounts.vocabulary}
          plagiarismIssues={suggestionCounts.plagiarism}
          similarityIssues={suggestionCounts.similarity}
          language={getWritingLanguageBase()}
          isAnalyzing={isAnalyzingText}
        />

        {/* AI Tools Section */}
        <div className="border-t bg-card">
          <Tabs defaultValue="ai-writer" className="w-full">
            <div className="flex flex-wrap items-center justify-between gap-4 border-b px-4 py-2">
                <TabsList className="flex-wrap h-auto">
                    <TabsTrigger value="ai-writer"><Wand2 className="h-4 w-4 mr-2"/>{t('aiRewriteTitle')}</TabsTrigger>
                    <TabsTrigger value="content-generator"><Edit3 className="h-4 w-4 mr-2"/>{t('aiTextGenerationTitle')}</TabsTrigger>
                    <TabsTrigger value="tone-analyzer"><Gauge className="h-4 w-4 mr-2"/>{t('aiToneAnalysisTitle')}</TabsTrigger>
                    {settings.enablePlagiarismDetection && <TabsTrigger value="plagiarism-detector"><ShieldCheck className="h-4 w-4 mr-2"/>{t('plagiarismDetectionTitle')}</TabsTrigger>}
                    <TabsTrigger value="ai-writing-detector"><BrainCircuit className="h-4 w-4 mr-2"/>{t('aiWritingDetectionTitle')}</TabsTrigger>
                    <TabsTrigger value="humanize-text"><UserCheck className="h-4 w-4 mr-2"/>{t('humanizeAiTextTitle')}</TabsTrigger>
                </TabsList>
            </div>

            <div className="p-4">
              <TabsContent value="ai-writer" className="mt-0">
                <AiRewriter currentText={editorValue} onApplyRewrite={handleApplyRewrite} writingMode={writingMode} direction={writingDirection} />
              </TabsContent>
              <TabsContent value="content-generator" className="mt-0">
                <AiTextGenerator onInsertText={handleInsertGeneratedText} />
              </TabsContent>
              <TabsContent value="tone-analyzer" className="mt-0">
                {settings.enableToneDetection && <AiToneAnalyzer currentText={editorValue} />}
              </TabsContent>
              <TabsContent value="plagiarism-detector" className="mt-0">
                {settings.enablePlagiarismDetection && <PlagiarismDetector currentText={editorValue} onResult={setPlagiarismResult} />}
              </TabsContent>
              <TabsContent value="ai-writing-detector" className="mt-0">
                <AiWritingDetector currentText={editorValue} />
              </TabsContent>
              <TabsContent value="humanize-text" className="mt-0">
                <HumanizeAiText currentText={editorValue} onInsertText={handleInsertGeneratedText} />
              </TabsContent>
            </div>
          </Tabs>
        </div>
      </div>
  );
}

export default function LinguaFlowPageContainer() {
  return (
    <AppShell>
      {(props) => <LinguaFlowPage {...props} />}
    </AppShell>
  );
}